package com.psbc.cpufp.ssl;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import javax.net.ssl.*;
import java.io.IOException;
import java.net.InetSocketAddress;
import java.net.Socket;
import java.security.KeyManagementException;
import java.security.NoSuchAlgorithmException;
import java.security.SecureRandom;
import java.security.cert.X509Certificate;
import java.util.Arrays;

/**
 * SSL诊断工具
 * 用于分析SSL握手过程、协议支持情况和常见问题
 */
public class SSLDiagnosticTool {
    
    private static final Logger log = LoggerFactory.getLogger(SSLDiagnosticTool.class);
    
    private static final String TARGET_HOST = "localhost";
    private static final int TARGET_PORT = 8443;
    
    public static void main(String[] args) {
        SSLDiagnosticTool tool = new SSLDiagnosticTool();
        
        log.info("开始SSL诊断...");
        log.info("目标服务器: {}:{}", TARGET_HOST, TARGET_PORT);
        
        // 1. 检查基本连接
        tool.checkBasicConnection();
        
        // 2. 检查支持的协议
        tool.checkSupportedProtocols();
        
        // 3. 检查支持的加密套件
        tool.checkSupportedCipherSuites();
        
        // 4. 测试不同协议版本的握手
        tool.testProtocolHandshakes();
        
        // 5. 分析服务器证书
        tool.analyzeServerCertificate();
        
        // 6. 模拟协议不一致问题
        tool.simulateProtocolMismatch();
    }
    
    /**
     * 检查基本TCP连接
     */
    public void checkBasicConnection() {
        log.info("=== 检查基本TCP连接 ===");
        
        try (Socket socket = new Socket()) {
            socket.connect(new InetSocketAddress(TARGET_HOST, TARGET_PORT), 5000);
            log.info("TCP连接成功 - {}:{}", TARGET_HOST, TARGET_PORT);
            log.info("本地地址: {}", socket.getLocalSocketAddress());
            log.info("远程地址: {}", socket.getRemoteSocketAddress());
        } catch (IOException e) {
            log.error("TCP连接失败: {}", e.getMessage());
        }
    }
    
    /**
     * 检查JVM支持的SSL/TLS协议
     */
    public void checkSupportedProtocols() {
        log.info("=== 检查JVM支持的SSL/TLS协议 ===");
        
        try {
            SSLContext context = SSLContext.getDefault();
            SSLSocketFactory factory = context.getSocketFactory();
            
            String[] supportedProtocols = factory.getSupportedCipherSuites();
            log.info("JVM支持的协议数量: {}", supportedProtocols.length);
            
            // 检查常用协议
            String[] commonProtocols = {"TLSv1", "TLSv1.1", "TLSv1.2", "TLSv1.3"};
            for (String protocol : commonProtocols) {
                try {
                    SSLContext.getInstance(protocol);
                    log.info("支持协议: {}", protocol);
                } catch (NoSuchAlgorithmException e) {
                    log.warn("不支持协议: {}", protocol);
                }
            }
            
        } catch (Exception e) {
            log.error("检查协议支持失败: {}", e.getMessage());
        }
    }
    
    /**
     * 检查支持的加密套件
     */
    public void checkSupportedCipherSuites() {
        log.info("=== 检查支持的加密套件 ===");
        
        try {
            SSLContext context = SSLContext.getDefault();
            SSLSocketFactory factory = context.getSocketFactory();
            
            String[] supportedCipherSuites = factory.getSupportedCipherSuites();
            String[] defaultCipherSuites = factory.getDefaultCipherSuites();
            
            log.info("支持的加密套件数量: {}", supportedCipherSuites.length);
            log.info("默认加密套件数量: {}", defaultCipherSuites.length);
            
            // 显示前10个默认加密套件
//            log.info("默认加密套件（前10个）:");
//            for (int i = 0; i < Math.min(10, defaultCipherSuites.length); i++) {
//                log.info("  {}", defaultCipherSuites[i]);
//            }
            // 支持的加密套件
            log.info("默认加密套件（前10个）:");
            for (int i = 0; i < supportedCipherSuites.length; i++) {
                log.info("  {}", supportedCipherSuites[i]);
            }
            
        } catch (Exception e) {
            log.error("检查加密套件失败: {}", e.getMessage());
        }
    }
    
    /**
     * 测试不同协议版本的SSL握手
     */
    public void testProtocolHandshakes() {
        log.info("=== 测试不同协议版本的SSL握手 ===");
        
        String[] protocols = {"TLSv1", "TLSv1.1", "TLSv1.2", "TLSv1.3"};
        
        for (String protocol : protocols) {
            testSSLHandshake(protocol);
        }
    }
    
    /**
     * 测试指定协议的SSL握手
     */
    private void testSSLHandshake(String protocol) {
        log.info("测试协议: {}", protocol);
        
        try {
            SSLContext sslContext = SSLContext.getInstance(protocol);
            
            // 配置信任所有证书
            TrustManager[] trustAllCerts = new TrustManager[] {
                new X509TrustManager() {
                    public X509Certificate[] getAcceptedIssuers() { return null; }
                    public void checkClientTrusted(X509Certificate[] certs, String authType) { }
                    public void checkServerTrusted(X509Certificate[] certs, String authType) { }
                }
            };
            
            sslContext.init(null, trustAllCerts, new SecureRandom());
            
            SSLSocketFactory factory = sslContext.getSocketFactory();
            
            try (SSLSocket sslSocket = (SSLSocket) factory.createSocket(TARGET_HOST, TARGET_PORT)) {
                // 设置启用的协议
                sslSocket.setEnabledProtocols(new String[]{protocol});
                
                // 开始SSL握手
                long startTime = System.currentTimeMillis();
                sslSocket.startHandshake();
                long endTime = System.currentTimeMillis();
                
                SSLSession session = sslSocket.getSession();
                
                log.info("协议 {} - 握手成功!", protocol);
                log.info("  握手时间: {}ms", (endTime - startTime));
                log.info("  协议版本: {}", session.getProtocol());
                log.info("  加密套件: {}", session.getCipherSuite());
                log.info("  对等主机: {}", session.getPeerHost());
                
            } catch (SSLHandshakeException e) {
                log.warn("协议 {} - 握手失败: {}", protocol, e.getMessage());
            }
            
        } catch (NoSuchAlgorithmException e) {
            log.error("协议 {} - 不支持: {}", protocol, e.getMessage());
        } catch (Exception e) {
            log.error("协议 {} - 错误: {}", protocol, e.getMessage());
        }
    }
    
    /**
     * 分析服务器证书
     */
    public void analyzeServerCertificate() {
        log.info("=== 分析服务器证书 ===");
        
        try {
            SSLContext sslContext = SSLContext.getInstance("TLSv1.2");
            
            // 配置自定义TrustManager来捕获证书信息
            TrustManager[] trustManagers = new TrustManager[] {
                new X509TrustManager() {
                    public X509Certificate[] getAcceptedIssuers() { return null; }
                    public void checkClientTrusted(X509Certificate[] certs, String authType) { }
                    
                    public void checkServerTrusted(X509Certificate[] certs, String authType) {
                        if (certs != null && certs.length > 0) {
                            X509Certificate cert = certs[0];
                            log.info("服务器证书信息:");
                            log.info("  主题: {}", cert.getSubjectDN());
                            log.info("  颁发者: {}", cert.getIssuerDN());
                            log.info("  序列号: {}", cert.getSerialNumber());
                            log.info("  有效期从: {}", cert.getNotBefore());
                            log.info("  有效期到: {}", cert.getNotAfter());
                            log.info("  签名算法: {}", cert.getSigAlgName());
                            log.info("  版本: {}", cert.getVersion());
                            
                            // 检查证书链
                            log.info("证书链长度: {}", certs.length);
                            for (int i = 0; i < certs.length; i++) {
                                log.info("  证书[{}]: {}", i, certs[i].getSubjectDN());
                            }
                        }
                    }
                }
            };
            
            sslContext.init(null, trustManagers, new SecureRandom());
            
            SSLSocketFactory factory = sslContext.getSocketFactory();
            
            try (SSLSocket sslSocket = (SSLSocket) factory.createSocket(TARGET_HOST, TARGET_PORT)) {
                sslSocket.startHandshake();
                log.info("证书分析完成");
            }
            
        } catch (Exception e) {
            log.error("证书分析失败: {}", e.getMessage());
        }
    }
    
    /**
     * 模拟协议不一致问题
     */
    public void simulateProtocolMismatch() {
        log.info("=== 模拟协议不一致问题 ===");
        
        // 场景1: 客户端只支持TLSv1，服务器要求TLSv1.2+
        log.info("场景1: 客户端TLSv1 vs 服务器TLSv1.2+");
        try {
            SSLContext sslContext = SSLContext.getInstance("TLSv1.2");
            sslContext.init(null, null, new SecureRandom());
            
            SSLSocketFactory factory = sslContext.getSocketFactory();
            
            try (SSLSocket sslSocket = (SSLSocket) factory.createSocket(TARGET_HOST, TARGET_PORT)) {
                // 强制只使用TLSv1
                sslSocket.setEnabledProtocols(new String[]{"TLSv1"});
                sslSocket.startHandshake();
                log.info("场景1 - 意外成功");
            } catch (SSLHandshakeException e) {
                log.info("场景1 - 预期失败: {}", e.getMessage());
            }
        } catch (Exception e) {
            log.error("场景1 - 错误: {}", e.getMessage());
        }
        
        // 场景2: 不兼容的加密套件
        log.info("场景2: 不兼容的加密套件");
        try {
            SSLContext sslContext = SSLContext.getInstance("TLSv1.2");
            sslContext.init(null, null, new SecureRandom());
            
            SSLSocketFactory factory = sslContext.getSocketFactory();
            
            try (SSLSocket sslSocket = (SSLSocket) factory.createSocket(TARGET_HOST, TARGET_PORT)) {
                // 设置一个可能不被服务器支持的加密套件
                sslSocket.setEnabledCipherSuites(new String[]{"TLS_RSA_WITH_NULL_MD5"});
                sslSocket.startHandshake();
                log.info("场景2 - 意外成功");
            } catch (SSLHandshakeException e) {
                log.info("场景2 - 预期失败: {}", e.getMessage());
            }
        } catch (Exception e) {
            log.error("场景2 - 错误: {}", e.getMessage());
        }
    }
}
