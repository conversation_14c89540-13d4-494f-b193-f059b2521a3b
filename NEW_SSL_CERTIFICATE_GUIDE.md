# 新SSL证书使用指南

## 📋 证书生成摘要

### ✅ 生成的文件
- **密钥库**: `src/main/resources/new_server_ks` (PKCS12格式)
- **公钥证书**: `src/main/resources/new_psbc_server.cer`
- **配置文件**: `src/main/resources/application-new-ssl.yml`
- **备份文件**: `src/main/resources/new_server_ks.old` (原JKS格式)

### 🔐 证书信息
- **别名**: `psbc-ssl-server`
- **密码**: `newpassword123`
- **有效期**: 2025-07-17 至 2035-07-15 (10年)
- **签名算法**: SHA256withRSA ✅ (安全)
- **密钥长度**: 2048位 RSA ✅ (安全)
- **格式**: PKCS12 ✅ (现代标准)
- **序列号**: 5B16853A

### 🌐 支持的主机名
- `localhost`
- `127.0.0.1`
- `**************`

## 🔄 新旧证书对比

| 项目 | 旧证书 | 新证书 | 状态 |
|------|--------|--------|------|
| 有效期 | 2016-03-16 至 2016-06-14 | 2025-07-17 至 2035-07-15 | ✅ 改进 |
| 签名算法 | SHA1withRSA | SHA256withRSA | ✅ 改进 |
| 密钥长度 | 1024位 | 2048位 | ✅ 改进 |
| 格式 | JKS | PKCS12 | ✅ 改进 |
| 状态 | ❌ 已过期 | ✅ 有效 | ✅ 改进 |

## 🚀 使用方法

### 方法1: 更新现有配置文件

编辑 `src/main/resources/application.yml`:

```yaml
server:
  ssl:
    key-store: classpath:new_server_ks
    key-store-type: PKCS12
    key-alias: psbc-ssl-server
    key-store-password: newpassword123
    key-password: newpassword123
```

### 方法2: 使用新的配置文件

启动应用时指定新配置:

```bash
java -jar target/agent-spf.jar --spring.config.additional-location=classpath:application-new-ssl.yml
```

### 方法3: 环境变量配置

```bash
export SSL_KEYSTORE_PATH=classpath:new_server_ks
export SSL_KEYSTORE_PASSWORD=newpassword123
export SSL_KEY_ALIAS=psbc-ssl-server
```

## 🧪 验证步骤

### 1. 启动应用
```bash
java -jar target/agent-spf.jar --spring.profiles.active=local
```

### 2. 验证HTTPS访问
```bash
# 健康检查
curl -k https://localhost:8443/xmysfzjjg/actuator/health

# WebService WSDL
curl -k https://localhost:8443/xmysfzjjg/wservices/IWebServiceService?wsdl
```

### 3. 检查证书信息
```bash
# 使用openssl检查
echo | openssl s_client -connect localhost:8443 -servername localhost 2>/dev/null | openssl x509 -noout -text

# 使用浏览器访问
# https://localhost:8443/xmysfzjjg/actuator/health
```

## 🔧 客户端配置

### WebService客户端信任新证书

如果客户端需要验证服务器证书，需要将新证书添加到信任库:

```bash
# 创建客户端信任库
keytool -import -alias psbc-server -file src/main/resources/new_psbc_server.cer -keystore client_truststore.jks -storepass trustpass

# 在客户端代码中使用
System.setProperty("javax.net.ssl.trustStore", "client_truststore.jks");
System.setProperty("javax.net.ssl.trustStorePassword", "trustpass");
```

### 更新WebService客户端配置

```java
// 在SSLConfigurationHelper中使用新证书
TLSClientParameters tlsParams = new TLSClientParameters();
tlsParams.setSecureSocketProtocol("TLSv1.2");
// 如果需要客户端证书认证，可以使用新的密钥库
```

## 🛡️ 安全优势

### ✅ 新证书的安全改进
1. **SHA256withRSA签名**: 替代不安全的SHA1withRSA
2. **2048位RSA密钥**: 替代不足的1024位密钥
3. **10年有效期**: 避免频繁更新证书
4. **PKCS12格式**: 使用现代标准格式
5. **SAN扩展**: 支持多个主机名和IP地址

### 🔒 支持的加密套件
新证书支持现代安全的加密套件:
- TLS_AES_256_GCM_SHA384 (TLS 1.3)
- TLS_ECDHE_RSA_WITH_AES_256_GCM_SHA384
- TLS_ECDHE_RSA_WITH_CHACHA20_POLY1305_SHA256
- 等等...

## 🚨 注意事项

### 1. 密码安全
- 新密码: `newpassword123`
- 生产环境建议使用更强的密码
- 考虑使用环境变量或密钥管理系统

### 2. 证书备份
- 原JKS格式备份: `new_server_ks.old`
- 建议将证书文件备份到安全位置

### 3. 客户端兼容性
- 确保客户端支持PKCS12格式
- 验证客户端支持SHA256withRSA签名
- 测试TLS 1.2/1.3协议兼容性

## 📝 故障排除

### 常见问题

#### 1. 证书格式错误
```
错误: java.security.KeyStoreException: PKCS12 KeyStore not available
解决: 确保JDK版本支持PKCS12格式 (JDK 8+)
```

#### 2. 密码错误
```
错误: java.security.UnrecoverableKeyException: Cannot recover key
解决: 检查密钥库密码和密钥密码是否正确
```

#### 3. 别名不存在
```
错误: java.lang.IllegalArgumentException: Alias does not exist
解决: 确认别名为 'psbc-ssl-server'
```

### 调试命令

```bash
# 列出密钥库内容
keytool -list -v -keystore src/main/resources/new_server_ks -storepass newpassword123

# 验证证书
keytool -printcert -file src/main/resources/new_psbc_server.cer

# 测试SSL连接
openssl s_client -connect localhost:8443 -servername localhost
```

## 📈 性能影响

### 预期改进
- **握手速度**: 2048位RSA密钥提供更好的安全性，握手时间略有增加但在可接受范围内
- **兼容性**: PKCS12格式提供更好的跨平台兼容性
- **安全性**: SHA256签名提供更强的安全保障

### 监控建议
- 监控SSL握手时间
- 检查证书过期时间
- 记录SSL连接错误

---

**生成时间**: 2025-07-17  
**证书有效期**: 10年 (至2035-07-15)  
**建议更新时间**: 2034年 (提前1年更新)
