# 项目部署指南 (最新版)

## 📦 打包完成信息

### ✅ 构建结果
- **主JAR文件**: `target/agent-spf.jar` (77.1 MB)
- **源码JAR**: `target/agent-spf-sources.jar` (116 KB)  
- **构建时间**: 2025-07-17 11:05:26
- **构建状态**: ✅ BUILD SUCCESS
- **构建耗时**: 1分42秒

### 🆕 本次打包新增功能
- ✅ **新SSL证书** (SHA256withRSA, 2048位, 有效期至2035年)
- ✅ **SSL配置优化** (消除重复配置)
- ✅ **TLS握手测试** (完整测试套件)
- ✅ **证书验证工具** (自动化验证)
- ✅ **配置文件更新** (使用新证书)

## 🚀 部署方式

### 标准部署 (推荐)
```bash
# 使用新SSL证书启动
java -jar target/agent-spf.jar

# 指定环境
java -jar target/agent-spf.jar --spring.profiles.active=local
```

### 生产环境部署
```bash
java -Xms1024m -Xmx2048m \
     -XX:+UseG1GC \
     -Dspring.profiles.active=prod \
     -jar target/agent-spf.jar
```

## 🔐 SSL证书信息

### 当前配置 (新证书)
- **密钥库**: `new_server_ks` (PKCS12格式)
- **别名**: `psbc-ssl-server`
- **密码**: `newpassword123`
- **有效期**: 2025-07-17 至 2035-07-15 (10年)
- **算法**: SHA256withRSA (安全)
- **密钥**: 2048位 RSA (安全)

### 支持的主机名
- localhost
- 127.0.0.1  
- **************

## 🧪 验证部署

### 1. 自动化测试
```bash
# 运行测试脚本
test-packaged-app.bat
```

### 2. 手动验证
```bash
# 启动应用
java -jar target/agent-spf.jar

# 健康检查
curl -k https://localhost:8443/xmysfzjjg/actuator/health

# WebService WSDL
curl -k https://localhost:8443/xmysfzjjg/wservices/IWebServiceService?wsdl

# SSL证书检查
echo | openssl s_client -connect localhost:8443 2>/dev/null | openssl x509 -noout -dates
```

## 🔒 安全建议

### 生产环境
1. **更改默认密码**
2. **限制网络访问**
3. **启用防火墙**
4. **定期更新证书** (2034年)

### 监控端点
- 健康检查: `/actuator/health`
- 指标监控: `/actuator/prometheus`

## 📊 性能参数

### JVM优化
```bash
# 开发环境
java -Xms512m -Xmx1024m -jar target/agent-spf.jar

# 生产环境  
java -Xms1024m -Xmx2048m -XX:+UseG1GC -jar target/agent-spf.jar
```

## 🚨 故障排除

### 常见问题
1. **SSL握手失败**: 检查证书配置
2. **端口冲突**: 使用 `--server.port=8444`
3. **内存不足**: 增加 `-Xmx` 参数
4. **证书过期**: 重新生成证书

### 调试命令
```bash
# SSL调试
java -Djavax.net.debug=ssl:handshake -jar target/agent-spf.jar

# 检查证书
keytool -list -v -keystore target/classes/new_server_ks -storepass newpassword123

# 检查端口
netstat -tulpn | grep 8443
```

## 📝 部署检查清单

- [ ] ✅ JAR文件生成成功 (77.1 MB)
- [ ] ✅ 新SSL证书已包含
- [ ] ✅ 配置文件已更新
- [ ] 🔄 应用启动测试
- [ ] 🔄 HTTPS连接验证
- [ ] 🔄 WebService访问测试
- [ ] 🔄 SSL证书验证
- [ ] 🔄 健康检查通过

---

**项目已成功打包，包含新的安全SSL证书和优化配置。**  
**建议先在测试环境验证，确认无误后部署到生产环境。**
