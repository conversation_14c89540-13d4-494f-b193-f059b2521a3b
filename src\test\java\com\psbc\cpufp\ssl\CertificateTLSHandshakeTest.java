package com.psbc.cpufp.ssl;

import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import javax.net.ssl.*;
import java.io.*;
import java.security.*;
import java.security.cert.CertificateFactory;
import java.security.cert.X509Certificate;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.TimeUnit;

import static org.junit.jupiter.api.Assertions.*;

/**
 * 测试邮政储蓄银行证书与server_ks的TLS握手兼容性
 */
public class CertificateTLSHandshakeTest {
    
    private static final Logger log = LoggerFactory.getLogger(CertificateTLSHandshakeTest.class);
    
//    private static final String SERVER_KEYSTORE_PATH = "src/main/resources/server_ks";
    private static final String SERVER_KEYSTORE_PATH = "src/main/resources/new_server_ks";
//    private static final String CLIENT_CERT_PATH = "src/main/resources/邮政储蓄银行.cer";
    private static final String CLIENT_CERT_PATH = "src/main/resources/new_psbc_server.cer";
//    private static final String KEYSTORE_PASSWORD = "123456";
    private static final String KEYSTORE_PASSWORD = "newpassword123";
    private static final String KEY_ALIAS = "psbc-ssl-server";
    
    private static final String TEST_HOST = "localhost";
    private static final int TEST_PORT = 8443;
    
    private SSLContext serverSSLContext;
    private SSLContext clientSSLContext;
    
    @BeforeEach
    void setUp() throws Exception {
        log.info("Setting up SSL contexts for certificate testing...");
        
        // 设置服务端SSL上下文
        serverSSLContext = createServerSSLContext();
        
        // 设置客户端SSL上下文
        clientSSLContext = createClientSSLContext();
        
        log.info("SSL contexts setup completed");
    }
    
    @Test
    @DisplayName("测试证书TLS握手 - 基本连接测试")
    void testBasicTLSHandshake() throws Exception {
        log.info("=== 开始基本TLS握手测试 ===");
        
        // 启动模拟SSL服务器
//        CompletableFuture<Boolean> serverResult = startMockSSLServer();
//
//        // 等待服务器启动
//        Thread.sleep(2000);
        
        // 执行客户端连接测试
        boolean handshakeSuccess = performClientHandshake();
        
        // 验证结果
        assertTrue(handshakeSuccess, "TLS握手应该成功");
        
        log.info("=== 基本TLS握手测试完成 ===");
    }
    
    @Test
    @DisplayName("测试证书兼容性 - 不同TLS协议版本")
    void testTLSProtocolCompatibility() throws Exception {
        log.info("=== 开始TLS协议兼容性测试 ===");
        
        String[] protocols = {"TLSv1.2", "TLSv1.1", "TLSv1"};
        
        for (String protocol : protocols) {
            log.info("测试协议版本: {}", protocol);
            
            try {
                boolean success = testProtocolHandshake(protocol);
                log.info("协议 {} 测试结果: {}", protocol, success ? "成功" : "失败");
                
                // TLSv1.2应该成功，其他可能失败（取决于JVM配置）
                if ("TLSv1.2".equals(protocol)) {
                    assertTrue(success, "TLSv1.2 握手应该成功");
                }
            } catch (Exception e) {
                log.warn("协议 {} 测试异常: {}", protocol, e.getMessage());
            }
        }
        
        log.info("=== TLS协议兼容性测试完成 ===");
    }
    
    @Test
    @DisplayName("测试证书验证 - 过期证书处理")
    void testExpiredCertificateHandling() throws Exception {
        log.info("=== 开始过期证书处理测试 ===");
        
        // 创建严格验证的SSL上下文
        SSLContext strictClientContext = createStrictClientSSLContext();
        
        CompletableFuture<Boolean> serverResult = startMockSSLServer();
        Thread.sleep(2000);
        
        // 使用严格验证的客户端连接
        boolean strictHandshakeSuccess = performStrictClientHandshake(strictClientContext);
        
        // 过期证书在严格模式下应该失败
        assertFalse(strictHandshakeSuccess, "过期证书在严格验证模式下应该失败");
        
        log.info("=== 过期证书处理测试完成 ===");
    }
    
    @Test
    @DisplayName("测试证书信息提取")
    void testCertificateInformationExtraction() throws Exception {
        log.info("=== 开始证书信息提取测试 ===");
        
        // 加载客户端证书
        X509Certificate clientCert = loadClientCertificate();
        assertNotNull(clientCert, "客户端证书应该能够加载");
        
        // 验证证书信息
        assertEquals("CN=localhost, OU=cn, O=cn, L=cn, ST=cn, C=cn", 
                    clientCert.getSubjectDN().toString());
        assertEquals("SHA1withRSA", clientCert.getSigAlgName());
        
        // 加载服务端证书
        X509Certificate serverCert = loadServerCertificate();
        assertNotNull(serverCert, "服务端证书应该能够加载");
        
        // 验证证书匹配
        assertEquals(clientCert.getSerialNumber(), serverCert.getSerialNumber(), 
                    "客户端和服务端证书序列号应该相同");
        
        log.info("客户端证书序列号: {}", clientCert.getSerialNumber().toString(16));
        log.info("服务端证书序列号: {}", serverCert.getSerialNumber().toString(16));
        log.info("证书匹配验证: 通过");
        
        log.info("=== 证书信息提取测试完成 ===");
    }
    
    private SSLContext createServerSSLContext() throws Exception {
        KeyStore keyStore = KeyStore.getInstance("JKS");
        try (FileInputStream fis = new FileInputStream(SERVER_KEYSTORE_PATH)) {
            keyStore.load(fis, KEYSTORE_PASSWORD.toCharArray());
        }
        
        KeyManagerFactory kmf = KeyManagerFactory.getInstance(KeyManagerFactory.getDefaultAlgorithm());
        kmf.init(keyStore, KEYSTORE_PASSWORD.toCharArray());
        
        SSLContext context = SSLContext.getInstance("TLS");
        context.init(kmf.getKeyManagers(), createTrustAllManager(), new SecureRandom());
        
        return context;
    }
    
    private SSLContext createClientSSLContext() throws Exception {
        SSLContext context = SSLContext.getInstance("TLS");
        context.init(null, createTrustAllManager(), new SecureRandom());
        return context;
    }
    
    private SSLContext createStrictClientSSLContext() throws Exception {
        SSLContext context = SSLContext.getInstance("TLS");
        context.init(null, null, new SecureRandom()); // 使用默认的严格验证
        return context;
    }
    
    private TrustManager[] createTrustAllManager() {
        return new TrustManager[] {
            new X509TrustManager() {
                public X509Certificate[] getAcceptedIssuers() { return new X509Certificate[0]; }
                public void checkClientTrusted(X509Certificate[] certs, String authType) {}
                public void checkServerTrusted(X509Certificate[] certs, String authType) {}
            }
        };
    }
    
    private CompletableFuture<Boolean> startMockSSLServer() {
        return CompletableFuture.supplyAsync(() -> {
            try {
                SSLServerSocketFactory factory = serverSSLContext.getServerSocketFactory();
                SSLServerSocket serverSocket = (SSLServerSocket) factory.createServerSocket(TEST_PORT);
                
                log.info("Mock SSL服务器启动在端口: {}", TEST_PORT);
                
                // 设置支持的协议
                serverSocket.setEnabledProtocols(new String[]{"TLSv1.2", "TLSv1.1", "TLSv1"});
                
                // 接受一个连接
                try (SSLSocket clientSocket = (SSLSocket) serverSocket.accept()) {
                    log.info("接受客户端连接: {}", clientSocket.getRemoteSocketAddress());
                    
                    // 执行握手
                    clientSocket.startHandshake();
                    SSLSession session = clientSocket.getSession();
                    
                    log.info("服务端握手成功:");
                    log.info("  协议: {}", session.getProtocol());
                    log.info("  加密套件: {}", session.getCipherSuite());
                    
                    // 简单的数据交换
                    PrintWriter out = new PrintWriter(clientSocket.getOutputStream(), true);
                    BufferedReader in = new BufferedReader(new InputStreamReader(clientSocket.getInputStream()));
                    
                    out.println("Hello from SSL Server");
                    String response = in.readLine();
                    log.info("收到客户端消息: {}", response);
                    
                    return true;
                } finally {
                    serverSocket.close();
                }
                
            } catch (Exception e) {
                log.error("Mock SSL服务器异常: {}", e.getMessage(), e);
                return false;
            }
        });
    }
    
    private boolean performClientHandshake() {
        try {
            SSLSocketFactory factory = clientSSLContext.getSocketFactory();
            
            try (SSLSocket socket = (SSLSocket) factory.createSocket(TEST_HOST, TEST_PORT)) {
                // 设置支持的协议
                socket.setEnabledProtocols(new String[]{"TLSv1.2", "TLSv1.1", "TLSv1"});
                
                log.info("客户端开始连接到 {}:{}", TEST_HOST, TEST_PORT);
                
                // 执行握手
                long startTime = System.currentTimeMillis();
                socket.startHandshake();
                long handshakeTime = System.currentTimeMillis() - startTime;
                
                SSLSession session = socket.getSession();
                
                log.info("客户端握手成功:");
                log.info("  握手时间: {}ms", handshakeTime);
                log.info("  协议: {}", session.getProtocol());
                log.info("  加密套件: {}", session.getCipherSuite());
                log.info("  对等主机: {}", session.getPeerHost());
                
                // 简单的数据交换
                PrintWriter out = new PrintWriter(socket.getOutputStream(), true);
                BufferedReader in = new BufferedReader(new InputStreamReader(socket.getInputStream()));
                
                String serverMessage = in.readLine();
                log.info("收到服务端消息: {}", serverMessage);
                out.println("Hello from SSL Client");
                
                return true;
            }
            
        } catch (Exception e) {
            log.error("客户端握手失败: {}", e.getMessage(), e);
            return false;
        }
    }
    
    private boolean testProtocolHandshake(String protocol) {
        try {
            SSLContext context = SSLContext.getInstance(protocol);
            context.init(null, createTrustAllManager(), new SecureRandom());
            
            SSLSocketFactory factory = context.getSocketFactory();
            
            try (SSLSocket socket = (SSLSocket) factory.createSocket(TEST_HOST, TEST_PORT)) {
                socket.setEnabledProtocols(new String[]{protocol});
                socket.startHandshake();
                
                SSLSession session = socket.getSession();
                log.info("协议 {} 握手成功，使用加密套件: {}", protocol, session.getCipherSuite());
                
                return true;
            }
            
        } catch (Exception e) {
            log.warn("协议 {} 握手失败: {}", protocol, e.getMessage());
            return false;
        }
    }
    
    private boolean performStrictClientHandshake(SSLContext context) {
        try {
            SSLSocketFactory factory = context.getSocketFactory();
            
            try (SSLSocket socket = (SSLSocket) factory.createSocket(TEST_HOST, TEST_PORT)) {
                socket.startHandshake();
                return true;
            }
            
        } catch (Exception e) {
            log.info("严格验证握手失败（预期行为）: {}", e.getMessage());
            return false;
        }
    }
    
    private X509Certificate loadClientCertificate() throws Exception {
        try (FileInputStream fis = new FileInputStream(CLIENT_CERT_PATH)) {
            CertificateFactory cf = CertificateFactory.getInstance("X.509");
            return (X509Certificate) cf.generateCertificate(fis);
        }
    }
    
    private X509Certificate loadServerCertificate() throws Exception {
        KeyStore keyStore = KeyStore.getInstance("JKS");
        try (FileInputStream fis = new FileInputStream(SERVER_KEYSTORE_PATH)) {
            keyStore.load(fis, KEYSTORE_PASSWORD.toCharArray());
        }
        return (X509Certificate) keyStore.getCertificate(KEY_ALIAS);
    }
}
