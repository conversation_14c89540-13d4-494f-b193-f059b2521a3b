#必填 该配置项对于bfes-agent-comm依赖的日志组件是必须的
server:
  port: 8443  # 确保端口号与客户端一致
  servlet:
    context-path: /xmysfzjjg  # 添加项目名前缀
  ssl:
    # 开启HTTPS访问
    enabled: true
    # 使用新生成的证书文件
    key-store: classpath:new_server_ks
    key-store-type: PKCS12  # 使用现代标准格式
    key-alias: psbc-ssl-server
    key-password: newpassword123
    key-store-password: newpassword123
    # SSL协议配置
    protocol: TLS
    # 启用的SSL协议版本
    enabled-protocols: TLSv1.2,TLSv1.3
    client-auth: none

cxf:
  path: /xmysfzjjg/wservices
  jaxws:
    servlet:
      load-on-startup: 1

#必填
#响应码的定位信息，通常为系统号前7位，，按照行内响应码标准，响应码通常为:一级分类+ 7位定位信息 + 二级分类 + 6位序列号，位于common-response-enum依赖
enum_config:
  prefix: 3531003

  #监控暴露端点
management:
  endpoints:
    web:
      exposure:
        include: "prometheus,health"

#必填
logging:
  config: classpath:logback-spring.xml

#必填
spring:
  application:
    name: agent-spf
  profiles:
    active: local
  main:
    allow-bean-definition-overriding: true
  jackson:
    date-format: yyyy-MM-dd HH:mm:ss
    time-zone: GMT+8
  servlet:
    multipart:
      max-file-size: 100MB
      max-request-size: 100MB

#必填
mybatis:
  mapper-locations: classpath:mapper/*.xml
  type-aliases-package: com.psbc.cpufp.entity
  configuration:
    map-underscore-to-camel-case: true

#必填
pagehelper:
  helper-dialect: mysql
  reasonable: true
  support-methods-arguments: true
  params: count=countSql

#必填
jasypt:
  encryptor:
    password: ${JASYPT_ENCRYPTOR_PASSWORD:mySecretKey}

#必填
bfes:
  agent:
    comm:
      #必填
      #系统号，按照行内标准，通常为7位数字
      system-no: 3531003
      #必填
      #系统名称
      system-name: 邮政储蓄银行信贷工厂智能分发系统
      #必填
      #系统简称
      system-short-name: agent-spf
      #必填
      #系统版本
      system-version: 1.0.0
      #必填
      #系统描述
      system-description: 邮政储蓄银行信贷工厂智能分发系统，负责信贷业务的智能路由和处理
      #必填
      #系统负责人
      system-owner: PSBC Development Team
      #必填
      #系统联系方式
      system-contact: <EMAIL>
      #必填
      #系统部署环境
      system-environment: ${spring.profiles.active:local}

# WebService客户端配置
webservice:
  client:
    # 使用HTTP进行测试，避免SSL握手问题
    url: http://localhost:8088/xmysfzjjg/wservices/IWebServiceService?wsdl
    # 如果需要使用HTTPS，请配置SSL参数
    ssl:
      trust-all: true
      verify-hostname: false
      protocol: TLSv1.2

# 新SSL证书信息
ssl:
  certificate:
    info: |
      新生成的SSL证书信息:
      - 别名: psbc-ssl-server
      - 有效期: 2025-07-17 至 2035-07-15 (10年)
      - 签名算法: SHA256withRSA (安全)
      - 密钥长度: 2048位 RSA (安全)
      - 密钥库格式: PKCS12 (现代标准)
      - 主机名: localhost, 127.0.0.1, **************
      - 序列号: 5b16853a
      - SHA256指纹: 71:1A:1D:58:60:A6:D1:B2:D6:05:CB:DD:FF:9D:C9:D0:03:57:31:02:E1:E6:76:CD:6C:55:29:64:BD:0D:D2:9B
