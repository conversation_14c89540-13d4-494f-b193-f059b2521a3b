package com.psbc.cpufp.config;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.boot.CommandLineRunner;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Component;

/**
 * SSL初始化器，在应用启动时配置SSL相关设置
 */
@Component
@Order(1) // 确保在其他组件之前执行
public class SSLInitializer implements CommandLineRunner {
    
    private static final Logger log = LoggerFactory.getLogger(SSLInitializer.class);
    
    @Override
    public void run(String... args) throws Exception {
        log.info("Initializing SSL configuration...");

        try {
            // 设置系统属性以支持更多的TLS协议
            System.setProperty("https.protocols", "TLSv1.2,TLSv1.3");
            System.setProperty("jdk.tls.client.protocols", "TLSv1.2,TLSv1.3");

            // 启用TLS调试（仅在需要时启用）
            // System.setProperty("javax.net.debug", "ssl,handshake");

            // 设置更宽松的SSL配置
            System.setProperty("com.sun.net.ssl.checkRevocation", "false");
            System.setProperty("sun.security.ssl.allowUnsafeRenegotiation", "true");

            log.info("SSL system properties configured successfully");

            // 注意：移除全局SSL验证禁用，让具体的客户端自己处理
            // 这样避免与WebService客户端的特定SSL配置冲突
            if (isDevEnvironment()) {
                log.info("Development environment detected - SSL verification will be handled by individual clients");
                // SSLConfigurationHelper.disableSSLVerificationGlobally(); // 注释掉避免冲突
            }

        } catch (Exception e) {
            log.error("Failed to initialize SSL configuration", e);
            // 不抛出异常，让应用继续启动
        }
    }
    
    /**
     * 检查是否为开发环境
     */
    private boolean isDevEnvironment() {
        String activeProfiles = System.getProperty("spring.profiles.active", "");
        return activeProfiles.contains("dev") || activeProfiles.contains("local") || activeProfiles.contains("test");
    }
}
